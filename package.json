{"name": "nextjs-ecommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.40.0", "@auth/prisma-adapter": "^2.10.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.10.1", "@stripe/stripe-js": "^7.4.0", "@tanstack/react-query": "^5.81.2", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "lucide-react": "^0.523.0", "next": "15.3.4", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "stripe": "^18.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "prettier": "^3.6.1", "prettier-plugin-tailwindcss": "^0.6.13", "prisma": "^6.10.1", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}