"use client";
import { useCart } from "@/components/cart-context";
import { useState } from "react";

export default function CheckoutPage() {
  const { items, clearCart } = useCart();
  const [orderId, setOrderId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const total = items.reduce((sum, item) => sum + item.price * item.quantity, 0);

  async function handlePlaceOrder() {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch("/api/orders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          items,
          total,
          shippingAddress: "123 Demo St, City, Country",
          userId: null, // You can wire up userId from session if needed
        }),
      });
      const data = await res.json();
      if (res.ok) {
        setOrderId(data.orderId);
        clearCart();
      } else {
        setError(data.error || "Order failed");
      }
    } catch (e) {
      setError("Order failed");
    } finally {
      setLoading(false);
    }
  }

  if (orderId) {
    return (
      <div className="space-y-8">
        <h1 className="text-3xl font-bold">Thank you for your order!</h1>
        <p className="text-gray-600">Your order has been placed successfully.</p>
        <div className="text-sm text-gray-500">Order ID: <span className="font-mono">{orderId}</span></div>
        <a href="/shop" className="text-blue-600 underline">Continue Shopping</a>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Checkout</h1>
      {items.length === 0 ? (
        <p className="text-gray-600">Your cart is empty.</p>
      ) : (
        <>
          <ul className="space-y-4">
            {items.map((item) => (
              <li key={item.id} className="flex items-center gap-4 border-b pb-2">
                <img src={item.image} alt={item.name} className="h-16 w-16 object-contain" />
                <div className="flex-1">
                  <div className="font-semibold">{item.name}</div>
                  <div className="text-sm text-gray-500">${item.price} x {item.quantity}</div>
                </div>
              </li>
            ))}
          </ul>
          <div className="mt-4 flex justify-between items-center">
            <span className="font-bold">Total: ${total.toFixed(2)}</span>
          </div>
          {error && <div className="text-red-500 text-sm">{error}</div>}
          <button
            onClick={handlePlaceOrder}
            className="mt-6 bg-black text-white rounded px-6 py-2 hover:bg-gray-800"
            disabled={loading}
          >
            {loading ? "Placing Order..." : "Place Order"}
          </button>
        </>
      )}
    </div>
  );
}
