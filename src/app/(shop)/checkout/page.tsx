"use client";
import { useCart } from "@/components/cart-context";
import { useState } from "react";

export default function CheckoutPage() {
  const { items, clearCart } = useCart();
  const [orderId, setOrderId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPayment, setShowPayment] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("card");
  const [shippingInfo, setShippingInfo] = useState({
    name: "",
    email: "",
    address: "",
    city: "",
    zipCode: "",
    country: ""
  });
  const total = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const shipping = 9.99;
  const tax = total * 0.08; // 8% tax
  const finalTotal = total + shipping + tax;

  async function handlePlaceOrder() {
    setLoading(true);
    setError(null);

    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    try {
      const shippingAddress = `${shippingInfo.name}, ${shippingInfo.address}, ${shippingInfo.city}, ${shippingInfo.zipCode}, ${shippingInfo.country}`;

      const res = await fetch("/api/orders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          items,
          total: finalTotal,
          shippingAddress,
          userId: null, // You can wire up userId from session if needed
          paymentMethod,
        }),
      });
      const data = await res.json();
      if (res.ok) {
        setOrderId(data.orderId);
        clearCart();
      } else {
        setError(data.error || "Order failed");
      }
    } catch (e) {
      setError("Order failed");
    } finally {
      setLoading(false);
    }
  }

  function handleShippingSubmit(e: React.FormEvent) {
    e.preventDefault();
    setShowPayment(true);
  }

  if (orderId) {
    return (
      <div className="space-y-8 max-w-2xl mx-auto text-center">
        <div className="text-green-600 text-6xl">✓</div>
        <h1 className="text-3xl font-bold text-green-600">Payment Successful!</h1>
        <p className="text-gray-600">Thank you for your order. Your payment has been processed successfully.</p>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="text-sm text-gray-500">Order ID: <span className="font-mono font-bold">{orderId}</span></div>
          <div className="text-sm text-gray-500 mt-1">Total Paid: <span className="font-bold">${finalTotal.toFixed(2)}</span></div>
        </div>
        <div className="flex gap-4 justify-center">
          <a href="/shop" className="bg-black text-white px-6 py-2 rounded hover:bg-gray-800">Continue Shopping</a>
          <a href="/orders" className="border border-gray-300 px-6 py-2 rounded hover:bg-gray-50">View Orders</a>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <h1 className="text-3xl font-bold">Checkout</h1>
      {items.length === 0 ? (
        <div className="text-center space-y-4">
          <p className="text-gray-600">Your cart is empty.</p>
          <a href="/shop" className="text-blue-600 underline">Continue Shopping</a>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Summary */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold">Order Summary</h2>
            <ul className="space-y-4">
              {items.map((item) => (
                <li key={item.id} className="flex items-center gap-4 border-b pb-2">
                  <img src={item.image} alt={item.name} className="h-16 w-16 object-contain" />
                  <div className="flex-1">
                    <div className="font-semibold">{item.name}</div>
                    <div className="text-sm text-gray-500">${item.price} x {item.quantity}</div>
                  </div>
                  <div className="font-semibold">${(item.price * item.quantity).toFixed(2)}</div>
                </li>
              ))}
            </ul>
            <div className="space-y-2 border-t pt-4">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>${total.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Shipping:</span>
                <span>${shipping.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax:</span>
                <span>${tax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-bold text-lg border-t pt-2">
                <span>Total:</span>
                <span>${finalTotal.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Checkout Form */}
          <div className="space-y-6">
            {!showPayment ? (
              <form onSubmit={handleShippingSubmit} className="space-y-4">
                <h2 className="text-xl font-semibold">Shipping Information</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="Full Name"
                    value={shippingInfo.name}
                    onChange={(e) => setShippingInfo({...shippingInfo, name: e.target.value})}
                    className="border rounded px-3 py-2"
                    required
                  />
                  <input
                    type="email"
                    placeholder="Email"
                    value={shippingInfo.email}
                    onChange={(e) => setShippingInfo({...shippingInfo, email: e.target.value})}
                    className="border rounded px-3 py-2"
                    required
                  />
                </div>
                <input
                  type="text"
                  placeholder="Address"
                  value={shippingInfo.address}
                  onChange={(e) => setShippingInfo({...shippingInfo, address: e.target.value})}
                  className="w-full border rounded px-3 py-2"
                  required
                />
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <input
                    type="text"
                    placeholder="City"
                    value={shippingInfo.city}
                    onChange={(e) => setShippingInfo({...shippingInfo, city: e.target.value})}
                    className="border rounded px-3 py-2"
                    required
                  />
                  <input
                    type="text"
                    placeholder="ZIP Code"
                    value={shippingInfo.zipCode}
                    onChange={(e) => setShippingInfo({...shippingInfo, zipCode: e.target.value})}
                    className="border rounded px-3 py-2"
                    required
                  />
                  <input
                    type="text"
                    placeholder="Country"
                    value={shippingInfo.country}
                    onChange={(e) => setShippingInfo({...shippingInfo, country: e.target.value})}
                    className="border rounded px-3 py-2"
                    required
                  />
                </div>
                <button
                  type="submit"
                  className="w-full bg-blue-600 text-white rounded px-6 py-3 hover:bg-blue-700"
                >
                  Continue to Payment
                </button>
              </form>
            ) : (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Payment Information</h2>
                <div className="space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      value="card"
                      checked={paymentMethod === "card"}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <span>Credit/Debit Card</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      value="paypal"
                      checked={paymentMethod === "paypal"}
                      onChange={(e) => setPaymentMethod(e.target.value)}
                    />
                    <span>PayPal</span>
                  </label>
                </div>

                {paymentMethod === "card" && (
                  <div className="space-y-3 p-4 border rounded">
                    <input
                      type="text"
                      placeholder="Card Number (4242 4242 4242 4242)"
                      className="w-full border rounded px-3 py-2"
                      defaultValue="4242 4242 4242 4242"
                    />
                    <div className="grid grid-cols-2 gap-4">
                      <input
                        type="text"
                        placeholder="MM/YY"
                        className="border rounded px-3 py-2"
                        defaultValue="12/25"
                      />
                      <input
                        type="text"
                        placeholder="CVC"
                        className="border rounded px-3 py-2"
                        defaultValue="123"
                      />
                    </div>
                  </div>
                )}

                {error && <div className="text-red-500 text-sm">{error}</div>}

                <div className="flex gap-4">
                  <button
                    onClick={() => setShowPayment(false)}
                    className="flex-1 border border-gray-300 text-gray-700 rounded px-6 py-3 hover:bg-gray-50"
                  >
                    Back to Shipping
                  </button>
                  <button
                    onClick={handlePlaceOrder}
                    className="flex-1 bg-green-600 text-white rounded px-6 py-3 hover:bg-green-700"
                    disabled={loading}
                  >
                    {loading ? "Processing Payment..." : `Pay $${finalTotal.toFixed(2)}`}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
