"use client";
import { useCart } from "@/components/cart-context";

export default function CartPage() {
  const { items, removeFromCart, clearCart, isLoaded } = useCart();
  const total = items.reduce((sum, item) => sum + item.price * item.quantity, 0);

  // Show loading state until cart is loaded to prevent hydration mismatch
  if (!isLoaded) {
    return (
      <div className="space-y-8">
        <h1 className="text-3xl font-bold">Your Cart</h1>
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <div className="text-gray-500 mt-2">Loading cart...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Your Cart</h1>
      {items.length === 0 ? (
        <p className="text-gray-600">Your cart is empty.</p>
      ) : (
        <>
          <ul className="space-y-4">
            {items.map((item) => (
              <li key={item.id} className="flex items-center gap-4 border-b pb-2">
                <img src={item.image} alt={item.name} className="h-16 w-16 object-contain" />
                <div className="flex-1">
                  <div className="font-semibold">{item.name}</div>
                  <div className="text-sm text-gray-500">${item.price} x {item.quantity}</div>
                </div>
                <button onClick={() => removeFromCart(item.id)} className="text-red-500">Remove</button>
              </li>
            ))}
          </ul>
          <div className="mt-4 flex justify-between items-center">
            <span className="font-bold">Total: ${total.toFixed(2)}</span>
            <button onClick={clearCart} className="text-sm text-gray-500 underline">Clear Cart</button>
          </div>
          <a href="/checkout" className="mt-6 inline-block bg-black text-white rounded px-6 py-2 hover:bg-gray-800">Proceed to Checkout</a>
        </>
      )}
    </div>
  );
}
