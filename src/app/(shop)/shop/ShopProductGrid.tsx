"use client";
import { useCart } from "@/components/cart-context";
import Link from "next/link";

export function ShopProductGrid({ products }: { products: any[] }) {
  const { addToCart } = useCart();
  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold">Shop</h1>
      <p className="text-gray-600">Browse all products here.</p>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {products.map((product) => (
          <div key={product.id} className="border rounded-lg p-4 flex flex-col">
            <Link href={`/shop/${product.id}`} className="hover:underline">
              <img src={product.image[0]} alt={product.name} className="h-40 object-contain mb-2" />
              <h2 className="text-xl font-semibold">{product.name}</h2>
            </Link>
            <p className="text-gray-500">{product.category?.name}</p>
            <p className="mt-2 text-lg font-bold">${product.price}</p>
            <p className="text-sm text-gray-600 flex-1">{product.description}</p>
            <button
              className="mt-4 bg-black text-white rounded px-4 py-2 hover:bg-gray-800"
              onClick={() => addToCart({
                id: product.id,
                name: product.name,
                price: Number(product.price),
                image: product.image[0],
                quantity: 1,
              })}
            >
              Add to Cart
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
