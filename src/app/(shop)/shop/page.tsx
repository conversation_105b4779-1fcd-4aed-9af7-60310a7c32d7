import { getProducts } from "@/lib/products";
import dynamic from "next/dynamic";

interface Product {
  id: string;
  name: string;
  price: number;
  image: string[];
  description: string;
  category: { name: string };
}

const ShopProductGrid = dynamic<{
  products: Product[];
}>(() => import("./ShopProductGrid").then(mod => mod.ShopProductGrid), { ssr: false });

export default async function ShopPage() {
  const productsRaw = await getProducts();
  const products = productsRaw.map((p: any) => ({
    ...p,
    price: Number(p.price),
  }));
  return <ShopProductGrid products={products} />;
}
