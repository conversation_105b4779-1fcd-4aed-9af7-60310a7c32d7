import { getProducts } from "@/lib/products";
import { ShopProductGrid } from "./ShopProductGrid";

interface Product {
  id: string;
  name: string;
  price: number;
  image: string[];
  description: string;
  category: { name: string };
}

export default async function ShopPage() {
  const productsRaw = await getProducts();
  const products = productsRaw.map((p: any) => ({
    ...p,
    price: Number(p.price),
  }));
  return <ShopProductGrid products={products} />;
}
