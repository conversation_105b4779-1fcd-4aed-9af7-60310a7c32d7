"use client";
import { useCart } from "@/components/cart-context";

export default function ProductDetailClient({ product }: { product: any }) {
  const { addToCart } = useCart();
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <img src={product.image[0]} alt={product.name} className="h-64 object-contain mx-auto" />
      <h1 className="text-3xl font-bold">{product.name}</h1>
      <p className="text-gray-500">{product.category?.name}</p>
      <p className="text-lg font-bold">${product.price}</p>
      <p className="text-gray-700">{product.description}</p>
      <button
        className="mt-4 bg-black text-white rounded px-6 py-2 hover:bg-gray-800"
        onClick={() => addToCart({
          id: product.id,
          name: product.name,
          price: Number(product.price),
          image: product.image[0],
          quantity: 1,
        })}
      >
        Add to Cart
      </button>
    </div>
  );
}
