import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  try {
    const { items, total, shippingAddress, userId, paymentMethod } = await req.json();

    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json({ error: "No items in order" }, { status: 400 });
    }

    if (!total || total <= 0) {
      return NextResponse.json({ error: "Invalid order total" }, { status: 400 });
    }

    // Mock payment step - simulate payment processing
    console.log(`Processing ${paymentMethod} payment for $${total}`);
    const paymentIntent = `mock_payment_${Date.now()}`;

    // Create order (userId can be null for guest orders)
    const order = await prisma.order.create({
      data: {
        userId: userId || null,
        total,
        shippingAddress: shippingAddress || "N/A",
        paymentIntent,
        items: {
          create: items.map((item: any) => ({
            productId: item.id,
            quantity: item.quantity,
            price: item.price,
          })),
        },
      },
      include: { items: true },
    });

    console.log(`Order created successfully: ${order.id}`);
    return NextResponse.json({ orderId: order.id });
  } catch (error) {
    console.error("Order creation failed:", error);
    return NextResponse.json({
      error: "Failed to create order. Please try again."
    }, { status: 500 });
  }
}
