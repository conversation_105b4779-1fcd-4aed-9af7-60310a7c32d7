import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function POST(req: Request) {
  const { items, total, shippingAddress, userId } = await req.json();
  if (!items || !Array.isArray(items) || items.length === 0) {
    return NextResponse.json({ error: "No items in order" }, { status: 400 });
  }
  // Mock payment step
  const paymentIntent = `mock_payment_${Date.now()}`;
  // Create order
  const order = await prisma.order.create({
    data: {
      userId,
      total,
      shippingAddress: shippingAddress || "N/A",
      paymentIntent,
      items: {
        create: items.map((item: any) => ({
          productId: item.id,
          quantity: item.quantity,
          price: item.price,
        })),
      },
    },
    include: { items: true },
  });
  return NextResponse.json({ orderId: order.id });
}
