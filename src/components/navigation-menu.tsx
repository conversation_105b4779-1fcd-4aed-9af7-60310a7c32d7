"use client";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { useCart } from "@/components/cart-context";

export function NavigationMenu() {
  const { data: session } = useSession();
  const { items, isLoaded } = useCart();
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <nav className="flex w-full items-center justify-between">
      <div className="flex items-center gap-6">
        <Link href="/" className="text-xl font-bold">
          Store
        </Link>
        <Link href="/shop" className="text-sm">
          Shop
        </Link>
        <Link href="/blog" className="text-sm">
          Blog
        </Link>
      </div>
      <div className="flex items-center gap-4">
        <Link href="/cart" className="text-sm relative">
          Cart
          {isLoaded && itemCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {itemCount}
            </span>
          )}
        </Link>
        {session ? (
          <>
            <Link href="/orders" className="text-sm">
              Orders
            </Link>
            <button onClick={() => signOut()} className="text-sm">
              Logout
            </button>
          </>
        ) : (
          <>
            <Link href="/login" className="text-sm">
              Login
            </Link>
            <Link href="/register" className="text-sm">
              Register
            </Link>
          </>
        )}
      </div>
    </nav>
  );
}
