import Link from "next/link";
import { useSession, signOut } from "next-auth/react";

export function NavigationMenu() {
  const { data: session } = useSession();

  return (
    <nav className="flex w-full items-center justify-between">
      <div className="flex items-center gap-6">
        <Link href="/" className="text-xl font-bold">
          Store
        </Link>
        <Link href="/shop" className="text-sm">
          Shop
        </Link>
        <Link href="/blog" className="text-sm">
          Blog
        </Link>
      </div>
      <div className="flex items-center gap-4">
        <Link href="/cart" className="text-sm">
          Cart
        </Link>
        {session ? (
          <>
            <Link href="/orders" className="text-sm">
              Orders
            </Link>
            <button onClick={() => signOut()} className="text-sm">
              Logout
            </button>
          </>
        ) : (
          <>
            <Link href="/login" className="text-sm">
              Login
            </Link>
            <Link href="/register" className="text-sm">
              Register
            </Link>
          </>
        )}
      </div>
    </nav>
  );
}
