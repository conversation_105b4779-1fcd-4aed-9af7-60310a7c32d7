import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  // Create categories
  const electronics = await prisma.category.upsert({
    where: { slug: "electronics" },
    update: {},
    create: {
      name: "Electronics",
      slug: "electronics",
      image: "/globe.svg",
    },
  });
  const clothing = await prisma.category.upsert({
    where: { slug: "clothing" },
    update: {},
    create: {
      name: "Clothing",
      slug: "clothing",
      image: "/vercel.svg",
    },
  });

  // Create products
  await prisma.product.createMany({
    data: [
      {
        name: "Smartphone",
        description: "A modern smartphone with all the latest features.",
        price: 699.99,
        image: ["/file.svg"],
        categoryId: electronics.id,
        stock: 50,
      },
      {
        name: "Laptop",
        description: "A powerful laptop for work and play.",
        price: 1299.99,
        image: ["/window.svg"],
        categoryId: electronics.id,
        stock: 30,
      },
      {
        name: "T-Shirt",
        description: "Comfortable cotton t-shirt.",
        price: 19.99,
        image: ["/vercel.svg"],
        categoryId: clothing.id,
        stock: 100,
      },
      {
        name: "<PERSON><PERSON>",
        description: "Stylish blue jeans.",
        price: 49.99,
        image: ["/next.svg"],
        categoryId: clothing.id,
        stock: 60,
      },
    ],
    skipDuplicates: true,
  });
}

main()
  .then(() => prisma.$disconnect())
  .catch((e) => {
    console.error(e);
    prisma.$disconnect();
    process.exit(1);
  });
